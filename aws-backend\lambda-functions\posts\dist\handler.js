"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const client_cognito_identity_provider_1 = require("@aws-sdk/client-cognito-identity-provider");
const client_dynamodb_1 = require("@aws-sdk/client-dynamodb");
const lib_dynamodb_1 = require("@aws-sdk/lib-dynamodb");
const uuid_1 = require("uuid");
const cognitoClient = new client_cognito_identity_provider_1.CognitoIdentityProviderClient({
    endpoint: process.env.AWS_ENDPOINT_URL || 'http://localhost:45660',
    region: process.env.AWS_DEFAULT_REGION || 'us-east-1',
    credentials: {
        accessKeyId: 'test',
        secretAccessKey: 'test'
    }
});
const dynamoClient = new client_dynamodb_1.DynamoDBClient({
    endpoint: process.env.AWS_ENDPOINT_URL || 'http://localhost:45660',
    region: process.env.AWS_DEFAULT_REGION || 'us-east-1',
    credentials: {
        accessKeyId: 'test',
        secretAccessKey: 'test'
    }
});
const docClient = lib_dynamodb_1.DynamoDBDocumentClient.from(dynamoClient);
const MEDIA_BUCKET = process.env.S3_BUCKET_MEDIA || 'gameflex-media-development';
function createCorsResponse(statusCode, body) {
    return {
        statusCode,
        headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Amz-Date, X-Api-Key, X-Amz-Security-Token'
        },
        body: JSON.stringify(body)
    };
}
async function getUserFromToken(accessToken) {
    try {
        const getUserCommand = new client_cognito_identity_provider_1.GetUserCommand({
            AccessToken: accessToken
        });
        const response = await cognitoClient.send(getUserCommand);
        const cognitoUserId = response.Username;
        const client = await dbPool.connect();
        try {
            const result = await client.query(`
                SELECT id, cognito_user_id, email, username, display_name, avatar_url
                FROM users 
                WHERE cognito_user_id = $1 AND is_active = true
            `, [cognitoUserId]);
            return result.rows[0] || null;
        }
        finally {
            client.release();
        }
    }
    catch (error) {
        console.error('Failed to get user from token:', error);
        return null;
    }
}
async function getPostsHandler(event) {
    try {
        const queryParams = event.queryStringParameters || {};
        const limit = Math.min(parseInt(queryParams.limit || '20'), 100);
        const offset = parseInt(queryParams.offset || '0');
        const channelId = queryParams.channel_id;
        const authorId = queryParams.author_id;
        const whereConditions = ["p.visibility = 'public'"];
        const params = [];
        let paramIndex = 1;
        if (channelId) {
            whereConditions.push(`p.channel_id = $${paramIndex++}`);
            params.push(channelId);
        }
        if (authorId) {
            whereConditions.push(`p.author_id = $${paramIndex++}`);
            params.push(authorId);
        }
        const whereClause = whereConditions.join(' AND ');
        const client = await dbPool.connect();
        try {
            const query = `
                SELECT 
                    p.id, p.content, p.like_count, p.comment_count, p.view_count,
                    p.created_at, p.updated_at,
                    u.id as author_id, u.username, u.display_name, u.avatar_url,
                    c.id as channel_id, c.name as channel_name,
                    m.id as media_id, m.filename, m.extension, m.type as media_type,
                    m.width, m.height, m.s3_bucket, m.s3_key
                FROM posts p
                JOIN users u ON p.author_id = u.id
                LEFT JOIN channels c ON p.channel_id = c.id
                LEFT JOIN media m ON p.media_id = m.id
                WHERE ${whereClause}
                ORDER BY p.created_at DESC
                LIMIT $${paramIndex++} OFFSET $${paramIndex++}
            `;
            params.push(limit, offset);
            const result = await client.query(query, params);
            const countQuery = `
                SELECT COUNT(*) 
                FROM posts p 
                WHERE ${whereClause}
            `;
            const countResult = await client.query(countQuery, params.slice(0, -2));
            const totalCount = parseInt(countResult.rows[0].count);
            const formattedPosts = result.rows.map((post) => ({
                id: post.id,
                content: post.content,
                like_count: post.like_count,
                comment_count: post.comment_count,
                view_count: post.view_count,
                created_at: post.created_at.toISOString(),
                updated_at: post.updated_at.toISOString(),
                author: {
                    id: post.author_id,
                    username: post.username,
                    display_name: post.display_name,
                    avatar_url: post.avatar_url
                },
                channel: post.channel_id ? {
                    id: post.channel_id,
                    name: post.channel_name
                } : null,
                media: post.media_id ? {
                    id: post.media_id,
                    filename: post.filename,
                    extension: post.extension,
                    type: post.media_type,
                    width: post.width,
                    height: post.height,
                    url: post.s3_key ? `http://localhost:45660/${post.s3_bucket}/${post.s3_key}` : null
                } : null
            }));
            return createCorsResponse(200, {
                posts: formattedPosts,
                pagination: {
                    limit,
                    offset,
                    total: totalCount,
                    has_more: offset + limit < totalCount
                }
            });
        }
        finally {
            client.release();
        }
    }
    catch (error) {
        console.error('Get posts handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
}
async function createPostHandler(event) {
    try {
        const authHeader = event.headers.Authorization || event.headers.authorization || '';
        if (!authHeader.startsWith('Bearer ')) {
            return createCorsResponse(401, {
                error: 'Authorization header required'
            });
        }
        const accessToken = authHeader.substring(7);
        const user = await getUserFromToken(accessToken);
        if (!user) {
            return createCorsResponse(401, {
                error: 'Invalid or expired token'
            });
        }
        const body = JSON.parse(event.body || '{}');
        const content = body.content?.trim();
        const channelId = body.channel_id;
        const mediaId = body.media_id;
        if (!content) {
            return createCorsResponse(400, {
                error: 'Content is required'
            });
        }
        if (channelId) {
            const client = await dbPool.connect();
            try {
                const result = await client.query(`
                    SELECT 1 FROM channel_members 
                    WHERE channel_id = $1 AND user_id = $2
                `, [channelId, user.id]);
                if (result.rows.length === 0) {
                    return createCorsResponse(403, {
                        error: 'You are not a member of this channel'
                    });
                }
            }
            finally {
                client.release();
            }
        }
        const client = await dbPool.connect();
        try {
            const postId = (0, uuid_1.v4)();
            const now = new Date();
            const result = await client.query(`
                INSERT INTO posts (id, content, media_id, author_id, channel_id, created_at, updated_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
                RETURNING id, content, like_count, comment_count, view_count, created_at, updated_at
            `, [postId, content, mediaId, user.id, channelId, now, now]);
            const post = result.rows[0];
            if (channelId) {
                await client.query(`
                    UPDATE channels 
                    SET post_count = post_count + 1, updated_at = $1
                    WHERE id = $2
                `, [now, channelId]);
            }
            return createCorsResponse(201, {
                message: 'Post created successfully',
                post: {
                    id: post.id,
                    content: post.content,
                    like_count: post.like_count,
                    comment_count: post.comment_count,
                    view_count: post.view_count,
                    created_at: post.created_at.toISOString(),
                    updated_at: post.updated_at.toISOString(),
                    author: {
                        id: user.id,
                        username: user.username,
                        display_name: user.display_name,
                        avatar_url: user.avatar_url
                    }
                }
            });
        }
        finally {
            client.release();
        }
    }
    catch (error) {
        console.error('Create post handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
}
async function getPostHandler(event) {
    try {
        const postId = event.pathParameters?.id;
        if (!postId) {
            return createCorsResponse(400, {
                error: 'Post ID is required'
            });
        }
        const client = await dbPool.connect();
        try {
            const result = await client.query(`
                SELECT 
                    p.id, p.content, p.like_count, p.comment_count, p.view_count,
                    p.created_at, p.updated_at,
                    u.id as author_id, u.username, u.display_name, u.avatar_url,
                    c.id as channel_id, c.name as channel_name,
                    m.id as media_id, m.filename, m.extension, m.type as media_type,
                    m.width, m.height, m.s3_bucket, m.s3_key
                FROM posts p
                JOIN users u ON p.author_id = u.id
                LEFT JOIN channels c ON p.channel_id = c.id
                LEFT JOIN media m ON p.media_id = m.id
                WHERE p.id = $1 AND p.visibility = 'public'
            `, [postId]);
            if (result.rows.length === 0) {
                return createCorsResponse(404, {
                    error: 'Post not found'
                });
            }
            const post = result.rows[0];
            await client.query(`
                UPDATE posts 
                SET view_count = view_count + 1 
                WHERE id = $1
            `, [postId]);
            const postData = {
                id: post.id,
                content: post.content,
                like_count: post.like_count,
                comment_count: post.comment_count,
                view_count: post.view_count + 1,
                created_at: post.created_at.toISOString(),
                updated_at: post.updated_at.toISOString(),
                author: {
                    id: post.author_id,
                    username: post.username,
                    display_name: post.display_name,
                    avatar_url: post.avatar_url
                },
                channel: post.channel_id ? {
                    id: post.channel_id,
                    name: post.channel_name
                } : null,
                media: post.media_id ? {
                    id: post.media_id,
                    filename: post.filename,
                    extension: post.extension,
                    type: post.media_type,
                    width: post.width,
                    height: post.height,
                    url: post.s3_key ? `http://localhost:45660/${post.s3_bucket}/${post.s3_key}` : null
                } : null
            };
            return createCorsResponse(200, {
                post: postData
            });
        }
        finally {
            client.release();
        }
    }
    catch (error) {
        console.error('Get post handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
}
const handler = async (event, context) => {
    if (event.httpMethod === 'OPTIONS') {
        return createCorsResponse(200, {});
    }
    const path = event.path;
    const method = event.httpMethod;
    try {
        if (path === '/posts' && method === 'GET') {
            return await getPostsHandler(event);
        }
        else if (path === '/posts' && method === 'POST') {
            return await createPostHandler(event);
        }
        else if (path.startsWith('/posts/') && method === 'GET') {
            return await getPostHandler(event);
        }
        else {
            return createCorsResponse(404, {
                error: 'Endpoint not found'
            });
        }
    }
    catch (error) {
        console.error('Lambda handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
};
exports.handler = handler;
