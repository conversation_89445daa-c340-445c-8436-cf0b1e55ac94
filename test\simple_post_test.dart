import 'package:flutter_test/flutter_test.dart';
import 'package:gameflex_mobile/services/aws_auth_service.dart';
import 'package:gameflex_mobile/services/aws_posts_service.dart';

void main() {
  group('Simple Post Creation Tests (AWS Backend)', () {
    late AwsAuthService authService;
    late AwsPostsService postsService;

    setUpAll(() async {
      // Initialize Flutter binding for tests
      TestWidgetsFlutterBinding.ensureInitialized();

      authService = AwsAuthService.instance;
      postsService = AwsPostsService.instance;

      // Authenticate as dev user
      try {
        final authResponse = await authService.signIn(
          email: '<EMAIL>',
          password: 'devpassword123',
        );
        if (authResponse.success) {
          print('✅ <NAME_EMAIL>');
        } else {
          print('❌ Authentication failed: ${authResponse.message}');
        }
      } catch (e) {
        print('❌ Authentication failed: $e');
      }
    });

    test('Test AWS Backend Connection', () async {
      try {
        final posts = await postsService.getPosts(limit: 1);
        print(
          '✅ AWS backend connection successful. Retrieved ${posts.length} posts',
        );
        expect(posts, isA<List>());
      } catch (e) {
        print('❌ AWS backend connection failed: $e');
        fail('AWS backend connection failed: $e');
      }
    });

    test('Test Authentication Status', () async {
      try {
        final isAuthenticated = authService.isAuthenticated;
        final currentUser = authService.currentUser;

        print('✅ Authentication check successful');
        print('Authenticated: $isAuthenticated');
        print('User: ${currentUser?.email}');

        expect(isAuthenticated, isTrue, reason: 'User should be authenticated');
        expect(
          currentUser,
          isNotNull,
          reason: 'Current user should not be null',
        );
      } catch (e) {
        print('❌ Authentication check failed: $e');
        fail('Authentication check failed: $e');
      }
    });

    test('Test Post Creation', () async {
      try {
        const testContent = 'Test post created by Flutter test (AWS Backend)';

        final createdPost = await postsService.createPost(content: testContent);

        if (createdPost != null) {
          print('✅ Post creation successful');
          print('Created post ID: ${createdPost.id}');
          print('Post content: ${createdPost.content}');

          expect(createdPost.id, isNotNull);
          expect(createdPost.content, equals(testContent));
          expect(createdPost.userId, isNotNull);
        } else {
          print('❌ Post creation returned null');
          fail('Post creation failed');
        }
      } catch (e) {
        print('❌ Post creation failed: $e');
        fail('Post creation failed: $e');
      }
    });
  });
}
