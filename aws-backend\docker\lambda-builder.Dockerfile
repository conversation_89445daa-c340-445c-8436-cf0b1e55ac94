# Lambda Builder Docker Image
# This image contains all tools needed to build TypeScript Lambda functions
FROM node:18-alpine

# Install system dependencies
RUN apk add --no-cache \
    zip \
    unzip \
    curl \
    bash \
    python3 \
    py3-pip \
    aws-cli

# Install global npm packages
RUN npm install -g typescript @types/node

# Create working directory
WORKDIR /workspace

# Create build script (will be mounted at runtime)
RUN mkdir -p /usr/local/bin

# Set default command
CMD ["bash", "/usr/local/bin/build-lambda.sh"]
