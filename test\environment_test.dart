import 'package:flutter_test/flutter_test.dart';
import 'package:gameflex_mobile/services/config_service.dart';

void main() {
  group('Environment Configuration Tests (AWS Backend)', () {
    late ConfigService configService;

    setUpAll(() async {
      TestWidgetsFlutterBinding.ensureInitialized();
      configService = ConfigService.instance;
    });

    test('Development environment should use local URLs', () async {
      // This test runs in development mode by default
      final serverUrl = await configService.getServerUrl();
      final environment = configService.getEnvironmentName();

      // Should use localhost for development
      expect(
        serverUrl.contains('localhost') || serverUrl.contains('127.0.0.1'),
        true,
        reason: 'Development should use local URLs, got: $serverUrl',
      );

      // Should not use production URL
      expect(
        serverUrl.contains('gameflex.io'),
        false,
        reason: 'Development should not use production URLs, got: $serverUrl',
      );

      expect(environment, equals('Development'));
    });

    test('Server URL options should be available', () {
      // Test that server URL options are available for development
      final options = configService.getServerUrlOptions();

      expect(
        options,
        isNotEmpty,
        reason: 'Server URL options should be available',
      );

      // Should have localhost option
      final hasLocalhost = options.any(
        (option) =>
            option.url.contains('localhost') ||
            option.url.contains('127.0.0.1'),
      );
      expect(hasLocalhost, true, reason: 'Should have localhost option');

      // Should have production option
      final hasProduction = options.any(
        (option) => option.url.contains('gameflex.io'),
      );
      expect(hasProduction, true, reason: 'Should have production option');
    });

    test('Environment detection should work correctly', () {
      final isDevelopment = configService.isDevelopment;
      final isStaging = configService.isStaging;
      final isProduction = configService.isProduction;

      // In test environment, should be development
      expect(isDevelopment, true, reason: 'Should be in development mode');
      expect(isStaging, false, reason: 'Should not be in staging mode');
      expect(isProduction, false, reason: 'Should not be in production mode');
    });

    test('Configuration caching should work', () {
      // Test that configuration can be cached and cleared
      configService.clearCache();

      // Should still work after cache clear
      final environment = configService.getEnvironmentName();
      expect(environment, isNotNull);
    });
  });
}
