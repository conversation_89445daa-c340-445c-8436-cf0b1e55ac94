import 'package:flutter_test/flutter_test.dart';
import 'package:gameflex_mobile/services/config_service.dart';

void main() {
  group('URL Configuration Tests (AWS Backend)', () {
    late ConfigService configService;

    setUpAll(() async {
      TestWidgetsFlutterBinding.ensureInitialized();
      configService = ConfigService.instance;
    });

    test('should provide correct server URL for development', () async {
      final serverUrl = await configService.getServerUrl();

      // Should be localhost for development
      expect(
        serverUrl.contains('localhost') || serverUrl.contains('127.0.0.1'),
        isTrue,
        reason: 'Development should use localhost URLs, got: $serverUrl',
      );
    });

    test('should provide server URL options', () {
      final options = configService.getServerUrlOptions();

      expect(options, isNotEmpty, reason: 'Should have server URL options');

      // Should have localhost option
      final hasLocalhost = options.any(
        (option) =>
            option.url.contains('localhost') ||
            option.url.contains('127.0.0.1'),
      );
      expect(hasLocalhost, isTrue, reason: 'Should have localhost option');

      // Should have production option
      final hasProduction = options.any(
        (option) => option.url.contains('gameflex.io'),
      );
      expect(hasProduction, isTrue, reason: 'Should have production option');
    });

    test('should detect environment correctly', () {
      final environment = configService.getEnvironmentName();
      final isDevelopment = configService.isDevelopment;

      expect(environment, isNotNull);
      expect(
        ['Development', 'Staging', 'Production'].contains(environment),
        isTrue,
      );

      // In test environment, should be development
      expect(isDevelopment, isTrue, reason: 'Should be in development mode');
    });

    test('should handle configuration caching', () {
      // Test cache clearing
      configService.clearCache();

      // Should still work after cache clear
      final environment = configService.getEnvironmentName();
      expect(environment, isNotNull);
    });
  });
}
