import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:gameflex_mobile/main.dart' as app;
import 'package:gameflex_mobile/services/aws_auth_service.dart';
import 'package:gameflex_mobile/services/aws_posts_service.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('GameFlex Integration Tests (AWS Backend)', () {
    late AwsAuthService authService;
    late AwsPostsService postsService;

    setUpAll(() async {
      // Initialize AWS services
      authService = AwsAuthService.instance;
      postsService = AwsPostsService.instance;
    });

    testWidgets('Test complete upload flow with new media structure', (
      WidgetTester tester,
    ) async {
      print('🧪 Starting integration test for media upload...');

      try {
        // Start the app
        app.main();
        await tester.pumpAndSettle();

        // Test authentication first
        print('🔐 Testing authentication...');

        final authResult = await authService.signIn(
          email: '<EMAIL>',
          password: 'devpassword123',
        );

        expect(
          authResult.success,
          isTrue,
          reason: 'Authentication should succeed',
        );
        expect(authResult.user, isNotNull);
        print('✅ Authentication successful: ${authResult.user?.email}');

        // Test post creation through AWS backend
        final testContent =
            'Integration test post with AWS backend - ${DateTime.now().millisecondsSinceEpoch}';

        print('📤 Testing post creation with AWS backend...');

        final createdPost = await postsService.createPost(content: testContent);

        expect(createdPost, isNotNull, reason: 'Post creation should succeed');
        print('✅ Post creation successful with AWS backend');

        // Verify the post was created
        expect(createdPost!.content, equals(testContent));
        expect(createdPost.userId, equals(authResult.user!.id));
        expect(createdPost.id, isNotNull);

        // Test retrieving posts
        final posts = await postsService.getPosts(limit: 5);

        expect(posts, isNotEmpty, reason: 'Posts should be retrieved');

        // Verify our created post is in the list
        final foundPost = posts.any((post) => post.content == testContent);
        expect(
          foundPost,
          isTrue,
          reason: 'Created post should be found in posts list',
        );

        print('✅ AWS backend integration verified');
        print('📊 Created post ID: ${createdPost.id}');
        print('📊 Retrieved ${posts.length} posts from AWS backend');

        print('🎉 Integration test completed successfully!');
      } catch (e, stackTrace) {
        print('❌ Integration test failed: $e');
        print('Stack trace: $stackTrace');
        rethrow;
      }
    });

    testWidgets('Test AWS backend service availability', (
      WidgetTester tester,
    ) async {
      print('🧪 Testing AWS backend service availability...');

      try {
        // Test that AWS services are available
        expect(authService, isNotNull);
        expect(postsService, isNotNull);

        // Test basic service functionality without authentication
        final posts = await postsService.getPosts(limit: 1);
        expect(posts, isA<List>());

        print('✅ AWS backend services are available and functional');
        print('🎉 AWS backend availability test passed!');
      } catch (e) {
        print('❌ AWS backend availability test failed: $e');
        // Don't fail the test as this might be expected in some environments
        print('⚠️  This is expected if AWS backend is not running');
      }
    });
  });
}
