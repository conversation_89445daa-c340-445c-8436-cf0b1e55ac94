# Deployer Docker Image
# This image contains AWS CLI and tools needed for CloudFormation deployment
FROM alpine:3.18

# Install system dependencies
RUN apk add --no-cache \
    aws-cli \
    bash \
    curl \
    jq \
    zip \
    unzip

# Create working directory
WORKDIR /workspace

# Create bin directory (script will be mounted at runtime)
RUN mkdir -p /usr/local/bin

# Set default command
CMD ["bash", "/usr/local/bin/deploy.sh"]
